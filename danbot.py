from pack import CARD, PURE_SEQUENCE, IMPURE_SEQUENCE, SET
from bot import BOT_INTERFACE
import random

class BOT(BOT_INTERFACE):
	def __init__(self, name: str) -> None:
		super().__init__(name)

	def nextMove(self, topCard: CARD) -> str:
		"""
		Simple bot logic:
		- If we have 13 cards and can potentially form valid sequences/sets, try to check
		- If the top card might be useful (same rank or consecutive rank), pick it
		- Otherwise, draw from deck
		"""
		print(f"{self.name} is thinking...")

		# Simple check: if we have exactly 13 cards, sometimes try to check rummy
		if len(self.hand) == 13 and random.random() < 0.1:  # 10% chance to check
			return "check"

		# Simple logic: pick the top card if it might be useful
		if self._is_card_useful(topCard):
			print(f"{self.name} picks the top card: {topCard}")
			return "pick"
		else:
			print(f"{self.name} draws from deck")
			return "draw"

	def takeCard(self) -> CARD:
		"""
		Simple logic: discard a random card that seems least useful
		"""
		if not self.hand:
			return CARD("Hearts", "2")  # Fallback

		# Try to keep cards that might form sequences or sets
		# For simplicity, just discard a random card
		card_to_discard = random.choice(self.hand)
		self.hand.remove(card_to_discard)
		print(f"{self.name} discards: {card_to_discard}")
		return card_to_discard

	def checkRummy(self) -> list[PURE_SEQUENCE | IMPURE_SEQUENCE | SET]:
		"""
		Simple rummy check: try to form basic sequences and sets
		This is a simplified implementation - a real bot would be more sophisticated
		"""
		print(f"{self.name} is checking rummy with hand: {self.hand}")

		# For simplicity, just try to group cards by suit and rank
		sequences_and_sets = []
		remaining_cards = self.hand.copy()

		# Try to form a simple pure sequence (3 consecutive cards of same suit)
		pure_seq = self._try_form_pure_sequence(remaining_cards)
		if pure_seq:
			sequences_and_sets.append(pure_seq)
			for card in pure_seq.cards:
				if card in remaining_cards:
					remaining_cards.remove(card)

		# Try to form sets or impure sequences with remaining cards
		while len(remaining_cards) >= 3:
			# Try to form a set (same rank, different suits)
			card_set = self._try_form_set(remaining_cards)
			if card_set:
				sequences_and_sets.append(card_set)
				for card in card_set.cards:
					if card in remaining_cards:
						remaining_cards.remove(card)
			else:
				# Try to form an impure sequence
				impure_seq = self._try_form_impure_sequence(remaining_cards)
				if impure_seq:
					sequences_and_sets.append(impure_seq)
					for card in impure_seq.cards:
						if card in remaining_cards:
							remaining_cards.remove(card)
				else:
					break

		return sequences_and_sets

	def _is_card_useful(self, card: CARD) -> bool:
		"""Check if a card might be useful for forming sequences or sets"""
		if card.suit == "Joker":
			return True  # Jokers are always useful

		# Check if we have cards of the same rank (for sets)
		same_rank_count = sum(1 for c in self.hand if c.rank == card.rank and c.suit != "Joker")
		if same_rank_count >= 1:
			return True

		# Check if we have cards of the same suit (for sequences)
		same_suit_cards = [c for c in self.hand if c.suit == card.suit and c.suit != "Joker"]
		if len(same_suit_cards) >= 2:
			return True

		return random.random() < 0.3  # 30% chance to pick anyway

	def _try_form_pure_sequence(self, cards: list[CARD]) -> PURE_SEQUENCE | None:
		"""Try to form a pure sequence from available cards"""
		# Group cards by suit
		suits = {}
		for card in cards:
			if card.suit != "Joker":
				if card.suit not in suits:
					suits[card.suit] = []
				suits[card.suit].append(card)

		# Try to find consecutive cards in each suit
		for suit, suit_cards in suits.items():
			if len(suit_cards) >= 3:
				# Sort by rank value for sequence checking
				rank_values = {"Ace": 1, "2": 2, "3": 3, "4": 4, "5": 5, "6": 6, "7": 7,
							  "8": 8, "9": 9, "10": 10, "Jack": 11, "Queen": 12, "King": 13}

				suit_cards.sort(key=lambda x: rank_values.get(x.rank, 0))

				# Try to find 3 consecutive cards
				for i in range(len(suit_cards) - 2):
					seq_cards = suit_cards[i:i+3]
					pure_seq = PURE_SEQUENCE(seq_cards)
					# We'll assume it's valid for this simple implementation
					return pure_seq

		return None

	def _try_form_set(self, cards: list[CARD]) -> SET | None:
		"""Try to form a set from available cards"""
		# Group cards by rank
		ranks = {}
		for card in cards:
			if card.suit != "Joker":
				if card.rank not in ranks:
					ranks[card.rank] = []
				ranks[card.rank].append(card)

		# Find ranks with at least 3 cards of different suits
		for rank, rank_cards in ranks.items():
			if len(rank_cards) >= 3:
				# Take first 3 cards of different suits
				different_suits = []
				used_suits = set()
				for card in rank_cards:
					if card.suit not in used_suits:
						different_suits.append(card)
						used_suits.add(card.suit)
					if len(different_suits) == 3:
						break

				if len(different_suits) == 3:
					return SET(different_suits)

		return None

	def _try_form_impure_sequence(self, cards: list[CARD]) -> IMPURE_SEQUENCE | None:
		"""Try to form an impure sequence (can include jokers)"""
		# For simplicity, just take any 3 cards and hope they form a valid impure sequence
		if len(cards) >= 3:
			return IMPURE_SEQUENCE(cards[:3])
		return None
