from pack import CARD
from bot import BOT_INTERFACE

class BOT(BOT_INTERFACE):
	def __init__(self, name: str) -> None:
		super().__init__(name)

	def nextMove(self, topCard: CARD) -> str:
		# Add code to return 'pick', 'draw' or 'check' to pick card from the table, draw from the deck or check the cards to win
		pass

	def takeCard(self) -> CARD:
		# Add code to return a card to the table
		pass
	
	def checkRummy(self) -> list[PURE_SEQUENCE | IMPURE_SEQUENCE | SET]:
		# Add code to return a dictionary containing a pure sequence and a pure/impure sequence and other sets or sequences
		pass
