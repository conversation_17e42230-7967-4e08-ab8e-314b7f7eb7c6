from abc import ABC, abstractmethod
from pack import CARD, PURE_SEQUENCE, IMPURE_SEQUENCE, SET

class BOT_INTERFACE(ABC):
	def __init__(self, name: str) -> None:
		self.name: str = name
		self.hand: list[CARD] = []

	@abstractmethod
	def nextMove(self, topCard: CARD) -> str:
		# Add code to return 'pick', 'draw' or 'check' to pick card from the table, draw from the deck or check the cards to win
		pass

	@abstractmethod
	def takeCard(self) -> CARD:
		# Add code to return a card to the table
		pass

	@abstractmethod
	def checkRummy(self) -> list[PURE_SEQUENCE | IMPURE_SEQUENCE | SET]:
		# Add code to return a list containing pure/impure sequencs and/or sets
		pass
