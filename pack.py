class CARD():
	def __init__(self, suit, rank="") -> None:
		self.suit: str = suit
		self.rank: str = rank
	
	def __repr__(self) -> str:
		return f"{self.rank} of {self.suit}" if self.suit != "Joker" else f"{self.suit}"

class PURE_SEQUENCE():
	def __init__(self, cards) -> None:
		self.cards: list[CARD] = cards

	def is_valid(self, wildCard: CARD) -> bool:
		if len(self.cards) < 3: return False
		deck = DECK()
		filtered = [c for c in self.cards if c.suit != "Joker" and c.rank != wildCard.rank]
		if len(filtered) != len(self.cards): return False

		suit = filtered[0].suit
		if any(c.suit != suit for c in filtered): return False

		ranks = deck.ranks
		vals = [ranks.index(c.rank) for c in filtered]

		return (
			self._is_consecutive(sorted(vals)) or
			self._is_consecutive(sorted([13 if v == 0 else v for v in vals]))
		)

	def _is_consecutive(self, values: list[int]) -> bool:
		return all(values[i] + 1 == values[i + 1] for i in range(len(values) - 1))

class IMPURE_SEQUENCE():
	def __init__(self, cards) -> None:
		self.cards: list[CARD] = cards

	def is_valid(self, wildCard: CARD) -> bool:
		if len(self.cards) < 3: return False
		deck = DECK()
		non_jokers = [c for c in self.cards if c.suit != "Joker" and c.rank != wildCard.rank]
		jokers = [c for c in self.cards if c.suit == "Joker" or c.rank == wildCard.rank]

		if not non_jokers: return False

		suit = non_jokers[0].suit
		if any(c.suit != suit for c in non_jokers): return False

		ranks = deck.ranks
		vals_low = sorted(ranks.index(c.rank) for c in non_jokers)
		vals_high = sorted([13 if v == 0 else v for v in vals_low])

		return (
			self._can_fill_gaps(vals_low, len(jokers)) or
			self._can_fill_gaps(vals_high, len(jokers))
		)

	def _can_fill_gaps(self, values: list[int], jokers: int) -> bool:
		gaps = sum(values[i + 1] - values[i] - 1 for i in range(len(values) - 1))
		return gaps <= jokers

class SET():
	def __init__(self, cards) -> None:
		self.cards: list[CARD] = cards

	def is_valid(self, wildCard: CARD) -> bool:
		if not (3 <= len(self.cards) <= 4): return False
		non_jokers = [c for c in self.cards if c.suit != "Joker" and c.rank != wildCard.rank]

		if not non_jokers: return False
		rank = non_jokers[0].rank

		if any(c.rank != rank for c in non_jokers): return False
		suits = [c.suit for c in non_jokers]

		if len(suits) != len(set(suits)): return False

		return True

class DECK():
	def __init__(self) -> None:
		self.suits: list[str] = ["Hearts", "Diamonds", "Clubs", "Spades"]
		self.ranks: list[str] = ["Ace", "2", "3", "4", "5", "6", "7", "8", "9", "10", "Jack", "Queen", "King"]
	
	def create_deck(self) -> list[CARD]:
		self.deck: list[CARD] = [CARD("Joker"), CARD("Joker")]
		for suit in self.suits:
			for rank in self.ranks:
				self.deck.append(CARD(suit, rank))
		return self.deck

class SCOREBOARD():
	def __init__(self, scores: dict[str, int]) -> None:
		self.scores = scores
	
	def set_score(self, player: str, score: int) -> None:
		self.scores[player] = score
	
	def get_score(self, player: str) -> int:
		return self.scores[player]
